# AI Studio 用户脚本监听修复 - 完整版

## 🔍 问题分析

通过对比您之前的 `userscripts/aistudio.user.js` 和当前的 TypeScript 实现，发现了以下关键问题：

### 1. 模型拦截问题
- **原脚本**: 直接重写 `xhr.onload`，立即拦截模型列表请求
- **新实现**: 使用 `addEventListener`，可能错过早期请求

### 2. 参数同步问题
- **原脚本**: 有完整的双向参数同步逻辑
- **新实现**: 缺少页面参数读取和同步功能

### 3. DOM选择器问题
- **原脚本**: 使用精确的 AI Studio 特定选择器
- **新实现**: 使用通用选择器，可能不匹配

## 🔧 完整修复方案

### 1. 修复模型拦截 - 完全按照原脚本逻辑

在 `src/api/request-interceptor.ts` 中的 `startImmediateInterception()` 方法：

```typescript
// 完全按照原脚本的方式 - 直接重写 xhr.onload
XMLHttpRequest.prototype.send = function (body?: Document | XMLHttpRequestBodyInit | null) {
  const xhr = this;
  const url = (xhr as any)._url;

  if (url === listModelsUrl) {
    xhr.onload = function () {
      if (xhr.readyState === 4 && xhr.status === 200) {
        try {
          const interceptedData = JSON.parse(xhr.response);
          logger.info('成功拦截到模型数据:', interceptedData);

          const models = self.parseModelsFromResponse(interceptedData);
          if (models.length > 0) {
            self.interceptedModels = models;
            logger.info(`拦截到模型列表: ${models.length} 个模型`);
            self.emit('modelsLoaded', models);
          }
        } catch (e) {
          logger.error('解析模型数据失败:', e);
        }
      }
    };
  }

  return self.originalXHRSend.apply(xhr, [body]);
};
```

### 2. 修复模型切换 - 使用原脚本的精确逻辑

在 `src/dom/dom-controller.ts` 中的 `switchModel()` 方法：

```typescript
// 1. 找到 AI Studio 页面的模型选择器并点击
const studioModelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
simulateClick(studioModelSelector);
await delay(500); // 等待下拉菜单动画

// 2. 从模型列表中找到对应的 displayName
const modelEntry = this.modelList.find(m =>
  m.id === modelId || m.id.endsWith(modelId) || m.name === modelId
);

// 3. 在展开的选项中找到目标模型
const allOptions = document.querySelectorAll(
  'div.cdk-overlay-pane mat-option, div.cdk-overlay-pane div[role="option"]'
);

// 4. 精确匹配模型选项
const optionTextElement = option.querySelector(
  '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
);
```

### 3. 修复参数设置 - 使用原脚本的选择器

```typescript
// 温度参数 - 使用精确选择器
let targetElement = document.querySelector(
  'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
) as HTMLInputElement;

// Top P 参数 - 查找 h3 标签
const allH3s = document.querySelectorAll('h3.gmat-body-medium');
for (const h3 of allH3s) {
  if (h3.textContent?.trim().toLowerCase() === 'top p') {
    const parentColumn = h3.closest('.settings-item-column');
    targetElement = parentColumn.querySelector('input[type="number"].slider-input');
  }
}

// 最大令牌数 - 使用 aria-label
let targetElement = document.querySelector(
  'input[aria-label="Maximum output tokens"]'
) as HTMLInputElement;

// 停止序列 - 处理 chips input
const stopInput = document.querySelector('input[aria-label="Add stop token"]');
const existingChips = document.querySelectorAll(
  'mat-chip-grid mat-chip-row button[aria-label*="Remove"]'
);
```

### 4. 添加参数读取功能

```typescript
// 获取当前页面的参数值
public getCurrentParameters(): GenerationConfig {
  const config: GenerationConfig = {};

  // 获取温度值
  const tempInput = document.querySelector(
    'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
  ) as HTMLInputElement;
  if (tempInput && tempInput.value) {
    config.temperature = parseFloat(tempInput.value);
  }

  // 获取 Top P、最大令牌数、停止序列等...
  return config;
}

// 获取当前选中的模型
public getCurrentModel(): string | null {
  const modelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
  const selectedText = modelSelector.textContent?.trim();
  const matchedModel = this.modelList.find(m =>
    m.displayName === selectedText || m.name === selectedText
  );
  return matchedModel ? matchedModel.id : null;
}
```

## 配置确认

确认 `vite.config.ts` 已正确配置：

```typescript
userscript: {
  // ...
  "run-at": "document-start",
  grant: [
    "GM_xmlhttpRequest",
    "GM_cookie",
    "GM_setValue",
    "GM_getValue",
    "GM_registerMenuCommand",
    "GM_setClipboard"
  ],
  connect: [
    "localhost",
    "alkalimakersuite-pa.clients6.google.com"
  ],
  noframes: true,
}
```

## 测试建议

1. **构建脚本**: `npm run build`
2. **安装脚本**: 将 `dist/gemini2api.user.js` 安装到 Tampermonkey
3. **访问页面**: 打开 `https://aistudio.google.com`
4. **检查日志**: 在浏览器控制台查看是否有 "拦截到模型列表" 的日志
5. **验证功能**: 确认调试面板显示模型列表

## 关键改进点

1. ✅ **在页面加载前启动拦截器**
2. ✅ **使用可靠的事件监听机制**
3. ✅ **精确匹配 AI Studio API URLs**
4. ✅ **同步模型数据和 DOM 初始化**
5. ✅ **保持与原脚本相同的运行时机**

这些修改确保了用户脚本能够像您之前的实现一样，在页面加载的早期阶段就开始监听网络请求，从而正确拦截到模型列表和其他 API 调用。
