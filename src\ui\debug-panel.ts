/**
 * AI Studio 2 API - Debug Panel UI
 *
 * 调试面板用户界面组件
 */

import type { ModelInfo, GenerationConfig, LogLevel } from '../types';
import { UI_CONFIG, MODEL_CONFIG, FEATURE_FLAGS } from '../config/constants';
import { logger, formatTimestamp } from '../utils/helpers';

export interface DebugPanelEvents {
  sendMessage: (message: string) => void;
  modelChanged: (modelId: string) => void;
  configChanged: (config: GenerationConfig) => void;
  connect: () => void;
  disconnect: () => void;
  clearLogs: () => void;
}

export class DebugPanel {
  private container: HTMLElement | null = null;
  private isVisible = false;
  private isMinimized = false;
  private eventHandlers: Partial<DebugPanelEvents> = {};
  private models: ModelInfo[] = [];
  private logs: Array<{ level: LogLevel; message: string; timestamp: string }> = [];

  constructor() {
    this.bindMethods();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.create = this.create.bind(this);
    this.show = this.show.bind(this);
    this.hide = this.hide.bind(this);
    this.toggle = this.toggle.bind(this);
    this.addLog = this.addLog.bind(this);
  }

  /**
   * 创建调试面板
   */
  public create(): void {
    if (this.container) {
      logger.warn('调试面板已存在');
      return;
    }

    if (!FEATURE_FLAGS.ENABLE_DEBUG_PANEL) {
      logger.info('调试面板功能已禁用');
      return;
    }

    logger.info('创建调试面板...');

    this.container = this.createContainer();
    this.createHeader();
    this.createContent();
    this.createStyles();

    document.body.appendChild(this.container);
    this.isVisible = true;

    logger.info('调试面板创建完成');
  }

  /**
   * 创建容器
   */
  private createContainer(): HTMLElement {
    const container = document.createElement('div');
    container.id = 'aistudio-debug-panel';
    container.style.cssText = `
      position: fixed;
      top: ${UI_CONFIG.DEBUG_PANEL.POSITION.TOP}px;
      right: ${UI_CONFIG.DEBUG_PANEL.POSITION.RIGHT}px;
      width: ${UI_CONFIG.DEBUG_PANEL.WIDTH}px;
      height: ${UI_CONFIG.DEBUG_PANEL.HEIGHT}px;
      min-width: ${UI_CONFIG.DEBUG_PANEL.MIN_WIDTH}px;
      min-height: ${UI_CONFIG.DEBUG_PANEL.MIN_HEIGHT}px;
      background-color: #282c34;
      border: 1px solid #61afef;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
      z-index: ${UI_CONFIG.DEBUG_PANEL.Z_INDEX};
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: #abb2bf;
      display: flex;
      flex-direction: column;
      resize: both;
      overflow: hidden;
    `;
    return container;
  }

  /**
   * 创建头部
   */
  private createHeader(): void {
    if (!this.container) return;

    const header = document.createElement('div');
    header.className = 'debug-panel-header';
    header.style.cssText = `
      padding: 10px 15px;
      background-color: #3e4451;
      border-bottom: 1px solid #61afef;
      cursor: grab;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      color: #c678dd;
      user-select: none;
    `;

    const title = document.createElement('span');
    title.textContent = 'AI Studio 2 API 调试面板';

    const controls = document.createElement('div');
    controls.style.cssText = 'display: flex; gap: 5px;';

    const minimizeBtn = this.createButton('—', () => this.toggleMinimize());
    const closeBtn = this.createButton('×', () => this.hide());

    controls.appendChild(minimizeBtn);
    controls.appendChild(closeBtn);

    header.appendChild(title);
    header.appendChild(controls);

    this.container.appendChild(header);
    this.setupDragAndDrop(header);
  }

  /**
   * 创建内容区域
   */
  private createContent(): void {
    if (!this.container) return;

    const content = document.createElement('div');
    content.className = 'debug-panel-content';
    content.style.cssText = `
      padding: 15px;
      flex-grow: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 15px;
    `;

    // 连接状态
    content.appendChild(this.createConnectionSection());

    // 消息发送
    content.appendChild(this.createMessageSection());

    // 模型选择
    content.appendChild(this.createModelSection());

    // 参数设置
    content.appendChild(this.createConfigSection());

    // 日志输出
    content.appendChild(this.createLogSection());

    this.container.appendChild(content);
  }

  /**
   * 创建连接状态区域
   */
  private createConnectionSection(): HTMLElement {
    const section = this.createSection('连接状态');

    const statusDiv = document.createElement('div');
    statusDiv.id = 'connection-status';
    statusDiv.style.cssText = `
      padding: 8px;
      border-radius: 4px;
      background-color: #e06c75;
      color: white;
      text-align: center;
      margin-bottom: 10px;
    `;
    statusDiv.textContent = '未连接';

    const buttonGroup = document.createElement('div');
    buttonGroup.style.cssText = 'display: flex; gap: 10px;';

    const connectBtn = this.createButton('连接', () => this.emit('connect'), '#98c379');
    const disconnectBtn = this.createButton('断开', () => this.emit('disconnect'), '#e06c75');

    buttonGroup.appendChild(connectBtn);
    buttonGroup.appendChild(disconnectBtn);

    section.appendChild(statusDiv);
    section.appendChild(buttonGroup);

    return section;
  }

  /**
   * 创建消息发送区域
   */
  private createMessageSection(): HTMLElement {
    const section = this.createSection('消息发送');

    const textarea = document.createElement('textarea');
    textarea.id = 'message-input';
    textarea.placeholder = '输入要发送的消息...';
    textarea.rows = 4;
    textarea.style.cssText = this.getInputStyle();

    const sendBtn = this.createButton('发送', () => {
      const message = textarea.value.trim();
      if (message) {
        this.emit('sendMessage', message);
        textarea.value = '';
      }
    }, '#61afef');

    section.appendChild(textarea);
    section.appendChild(sendBtn);

    return section;
  }

  /**
   * 创建模型选择区域
   */
  private createModelSection(): HTMLElement {
    const section = this.createSection('模型选择');

    const select = document.createElement('select');
    select.id = 'model-select';
    select.style.cssText = this.getInputStyle();

    select.addEventListener('change', () => {
      if (select.value) {
        this.emit('modelChanged', select.value);
      }
    });

    section.appendChild(select);
    this.updateModelList();

    return section;
  }

  /**
   * 创建配置区域
   */
  private createConfigSection(): HTMLElement {
    const section = this.createSection('生成参数');

    // 温度
    const tempGroup = this.createInputGroup(
      '温度 (Temperature)',
      'number',
      'temperature-input',
      MODEL_CONFIG.DEFAULT_TEMPERATURE.toString(),
      {
        min: MODEL_CONFIG.TEMPERATURE_MIN.toString(),
        max: MODEL_CONFIG.TEMPERATURE_MAX.toString(),
        step: MODEL_CONFIG.TEMPERATURE_STEP.toString(),
      }
    );

    // Top P
    const topPGroup = this.createInputGroup(
      'Top P',
      'number',
      'top-p-input',
      MODEL_CONFIG.DEFAULT_TOP_P.toString(),
      {
        min: MODEL_CONFIG.TOP_P_MIN.toString(),
        max: MODEL_CONFIG.TOP_P_MAX.toString(),
        step: MODEL_CONFIG.TOP_P_STEP.toString(),
      }
    );

    // 最大令牌数
    const maxTokensGroup = this.createInputGroup(
      '最大输出令牌数',
      'number',
      'max-tokens-input',
      MODEL_CONFIG.DEFAULT_MAX_TOKENS.toString(),
      {
        min: MODEL_CONFIG.MAX_TOKENS_MIN.toString(),
        max: MODEL_CONFIG.MAX_TOKENS_MAX.toString(),
        step: MODEL_CONFIG.MAX_TOKENS_STEP.toString(),
      }
    );

    // 停止序列
    const stopSeqGroup = this.createInputGroup(
      '停止序列 (每行一个)',
      'textarea',
      'stop-sequences-input',
      '',
      { rows: '3' }
    );

    section.appendChild(tempGroup);
    section.appendChild(topPGroup);
    section.appendChild(maxTokensGroup);
    section.appendChild(stopSeqGroup);

    // 监听参数变化
    this.setupConfigListeners();

    return section;
  }

  /**
   * 创建日志区域
   */
  private createLogSection(): HTMLElement {
    const section = this.createSection('操作日志');

    const logContainer = document.createElement('div');
    logContainer.id = 'log-container';
    logContainer.style.cssText = `
      background-color: #1e2127;
      border: 1px solid #4b5263;
      border-radius: 4px;
      padding: 10px;
      height: 200px;
      overflow-y: auto;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 12px;
      white-space: pre-wrap;
    `;

    const clearBtn = this.createButton('清除日志', () => {
      this.clearLogs();
      this.emit('clearLogs');
    }, '#e5c07b');

    section.appendChild(logContainer);
    section.appendChild(clearBtn);

    return section;
  }

  /**
   * 创建区域
   */
  private createSection(title: string): HTMLElement {
    const section = document.createElement('div');
    section.className = 'debug-section';
    section.style.cssText = `
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px dashed #4b5263;
    `;

    const titleEl = document.createElement('h4');
    titleEl.textContent = title;
    titleEl.style.cssText = `
      margin: 0 0 10px 0;
      color: #98c379;
      font-size: 14px;
    `;

    section.appendChild(titleEl);
    return section;
  }

  /**
   * 创建输入组
   */
  private createInputGroup(
    label: string,
    type: string,
    id: string,
    defaultValue: string,
    attrs: Record<string, string> = {}
  ): HTMLElement {
    const group = document.createElement('div');
    group.style.cssText = 'margin-bottom: 10px;';

    const labelEl = document.createElement('label');
    labelEl.textContent = label;
    labelEl.style.cssText = `
      display: block;
      margin-bottom: 5px;
      font-size: 12px;
      color: #98c379;
    `;

    let input: HTMLElement;
    if (type === 'textarea') {
      input = document.createElement('textarea');
      (input as HTMLTextAreaElement).value = defaultValue;
    } else {
      input = document.createElement('input');
      (input as HTMLInputElement).type = type;
      (input as HTMLInputElement).value = defaultValue;
    }

    input.id = id;
    input.style.cssText = this.getInputStyle();

    // 设置属性
    Object.entries(attrs).forEach(([key, value]) => {
      input.setAttribute(key, value);
    });

    group.appendChild(labelEl);
    group.appendChild(input);

    return group;
  }

  /**
   * 创建按钮
   */
  private createButton(
    text: string,
    onClick: () => void,
    color: string = '#61afef'
  ): HTMLButtonElement {
    const button = document.createElement('button');
    button.textContent = text;
    button.style.cssText = `
      background-color: ${color};
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: opacity 0.2s;
    `;

    button.addEventListener('click', onClick);
    button.addEventListener('mouseenter', () => {
      button.style.opacity = '0.8';
    });
    button.addEventListener('mouseleave', () => {
      button.style.opacity = '1';
    });

    return button;
  }

  /**
   * 获取输入框样式
   */
  private getInputStyle(): string {
    return `
      width: 100%;
      padding: 8px;
      border: 1px solid #5c6370;
      border-radius: 4px;
      background-color: #3e4451;
      color: #abb2bf;
      font-size: 12px;
      box-sizing: border-box;
      resize: vertical;
    `;
  }

  /**
   * 创建样式
   */
  private createStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      #aistudio-debug-panel input:focus,
      #aistudio-debug-panel textarea:focus,
      #aistudio-debug-panel select:focus {
        border-color: #61afef;
        outline: none;
        box-shadow: 0 0 0 2px rgba(97, 175, 239, 0.2);
      }

      #aistudio-debug-panel::-webkit-scrollbar {
        width: 8px;
      }

      #aistudio-debug-panel::-webkit-scrollbar-track {
        background: #3e4451;
      }

      #aistudio-debug-panel::-webkit-scrollbar-thumb {
        background: #5c6370;
        border-radius: 4px;
      }

      #aistudio-debug-panel::-webkit-scrollbar-thumb:hover {
        background: #61afef;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 设置拖拽功能
   */
  private setupDragAndDrop(header: HTMLElement): void {
    let isDragging = false;
    let offsetX = 0;
    let offsetY = 0;

    header.addEventListener('mousedown', (e) => {
      isDragging = true;
      const rect = this.container!.getBoundingClientRect();
      offsetX = e.clientX - rect.left;
      offsetY = e.clientY - rect.top;
      header.style.cursor = 'grabbing';
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging || !this.container) return;

      const x = e.clientX - offsetX;
      const y = e.clientY - offsetY;

      this.container.style.left = `${Math.max(0, x)}px`;
      this.container.style.top = `${Math.max(0, y)}px`;
      this.container.style.right = 'auto';
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        header.style.cursor = 'grab';
      }
    });
  }

  /**
   * 设置配置监听器
   */
  private setupConfigListeners(): void {
    const inputs = ['temperature-input', 'top-p-input', 'max-tokens-input', 'stop-sequences-input'];

    inputs.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => this.emitConfigChange());
      }
    });
  }

  /**
   * 触发配置变化事件
   */
  private emitConfigChange(): void {
    const config: GenerationConfig = {};

    const tempInput = document.getElementById('temperature-input') as HTMLInputElement;
    if (tempInput?.value) {
      config.temperature = parseFloat(tempInput.value);
    }

    const topPInput = document.getElementById('top-p-input') as HTMLInputElement;
    if (topPInput?.value) {
      config.topP = parseFloat(topPInput.value);
    }

    const maxTokensInput = document.getElementById('max-tokens-input') as HTMLInputElement;
    if (maxTokensInput?.value) {
      config.maxOutputTokens = parseInt(maxTokensInput.value, 10);
    }

    const stopSeqInput = document.getElementById('stop-sequences-input') as HTMLTextAreaElement;
    if (stopSeqInput?.value) {
      config.stopSequences = stopSeqInput.value
        .split('\n')
        .map(s => s.trim())
        .filter(s => s.length > 0);
    }

    this.emit('configChanged', config);
  }

  /**
   * 更新连接状态
   */
  public updateConnectionStatus(connected: boolean, message?: string): void {
    const statusEl = document.getElementById('connection-status');
    if (statusEl) {
      statusEl.textContent = message || (connected ? '已连接' : '未连接');
      statusEl.style.backgroundColor = connected ? '#98c379' : '#e06c75';
    }
  }

  /**
   * 设置模型列表
   */
  public setModels(models: ModelInfo[]): void {
    this.models = models;
    this.updateModelList();
  }

  /**
   * 更新模型列表
   */
  private updateModelList(): void {
    const select = document.getElementById('model-select') as HTMLSelectElement;
    if (!select) return;

    select.innerHTML = '<option value="">-- 选择模型 --</option>';

    this.models.forEach(model => {
      const option = document.createElement('option');
      option.value = model.id;
      option.textContent = model.displayName || model.name;
      select.appendChild(option);
    });
  }

  /**
   * 从页面更新参数值（双向同步）
   */
  public updateParametersFromPage(params: GenerationConfig, currentModel?: string | null): void {
    try {
      // 更新模型选择
      if (currentModel) {
        const modelSelect = document.getElementById('model-select') as HTMLSelectElement;
        if (modelSelect && modelSelect.value !== currentModel) {
          modelSelect.value = currentModel;
        }
      }

      // 更新温度
      if (params.temperature !== undefined) {
        const tempInput = document.getElementById('temperature-input') as HTMLInputElement;
        if (tempInput && parseFloat(tempInput.value) !== params.temperature) {
          tempInput.value = params.temperature.toString();
        }
      }

      // 更新 Top P
      if (params.topP !== undefined) {
        const topPInput = document.getElementById('top-p-input') as HTMLInputElement;
        if (topPInput && parseFloat(topPInput.value) !== params.topP) {
          topPInput.value = params.topP.toString();
        }
      }

      // 更新最大令牌数
      if (params.maxOutputTokens !== undefined) {
        const maxTokensInput = document.getElementById('max-tokens-input') as HTMLInputElement;
        if (maxTokensInput && parseInt(maxTokensInput.value) !== params.maxOutputTokens) {
          maxTokensInput.value = params.maxOutputTokens.toString();
        }
      }

      // 更新停止序列
      if (params.stopSequences !== undefined) {
        const stopSeqInput = document.getElementById('stop-sequences-input') as HTMLTextAreaElement;
        if (stopSeqInput) {
          const currentValue = stopSeqInput.value.split('\n').map(s => s.trim()).filter(s => s.length > 0);
          const newValue = params.stopSequences;

          // 只有当值真的不同时才更新
          if (JSON.stringify(currentValue) !== JSON.stringify(newValue)) {
            stopSeqInput.value = newValue.join('\n');
          }
        }
      }
    } catch (error) {
      logger.debug('更新调试面板参数失败:', error);
    }
  }

  /**
   * 添加日志
   */
  public addLog(level: LogLevel, message: string): void {
    const timestamp = formatTimestamp();
    this.logs.push({ level, message, timestamp });

    // 限制日志数量
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500);
    }

    this.updateLogDisplay();
  }

  /**
   * 更新日志显示
   */
  private updateLogDisplay(): void {
    const container = document.getElementById('log-container');
    if (!container) return;

    const logText = this.logs
      .slice(-100) // 只显示最近100条
      .map(log => `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}`)
      .join('\n');

    container.textContent = logText;
    container.scrollTop = container.scrollHeight;
  }

  /**
   * 清除日志
   */
  public clearLogs(): void {
    this.logs = [];
    this.updateLogDisplay();
  }

  /**
   * 切换最小化
   */
  private toggleMinimize(): void {
    if (!this.container) return;

    const content = this.container.querySelector('.debug-panel-content') as HTMLElement;
    const button = this.container.querySelector('button') as HTMLButtonElement;

    if (this.isMinimized) {
      content.style.display = 'flex';
      button.textContent = '—';
      this.container.style.height = `${UI_CONFIG.DEBUG_PANEL.HEIGHT}px`;
    } else {
      content.style.display = 'none';
      button.textContent = '□';
      this.container.style.height = 'auto';
    }

    this.isMinimized = !this.isMinimized;
  }

  /**
   * 显示面板
   */
  public show(): void {
    if (this.container) {
      this.container.style.display = 'flex';
      this.isVisible = true;
    }
  }

  /**
   * 隐藏面板
   */
  public hide(): void {
    if (this.container) {
      this.container.style.display = 'none';
      this.isVisible = false;
    }
  }

  /**
   * 切换显示/隐藏
   */
  public toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 销毁面板
   */
  public destroy(): void {
    if (this.container) {
      this.container.remove();
      this.container = null;
      this.isVisible = false;
    }
  }

  /**
   * 注册事件处理器
   */
  public on<K extends keyof DebugPanelEvents>(
    event: K,
    handler: DebugPanelEvents[K]
  ): void {
    this.eventHandlers[event] = handler;
  }

  /**
   * 触发事件
   */
  private emit<K extends keyof DebugPanelEvents>(
    event: K,
    ...args: Parameters<NonNullable<DebugPanelEvents[K]>>
  ): void {
    const handler = this.eventHandlers[event];
    if (handler) {
      try {
        (handler as any)(...args);
      } catch (error) {
        logger.error(`调试面板事件处理器执行失败 (${event}):`, error);
      }
    }
  }
}
