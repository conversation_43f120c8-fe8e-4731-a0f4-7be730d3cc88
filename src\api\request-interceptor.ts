/**
 * AI Studio 2 API - Request Interceptor
 *
 * 拦截和处理 AI Studio 的 API 请求和响应
 */

import type {
  ModelInfo,
  GenerateContentRequest,
  GenerateContentResponse
} from '../types';
import { logger, isValid<PERSON>son } from '../utils/helpers';

export interface InterceptorEvents {
  modelsLoaded: (models: ModelInfo[]) => void;
  requestSent: (request: GenerateContentRequest) => void;
  responseReceived: (response: GenerateContentResponse) => void;
  streamChunk: (chunk: string) => void;
  error: (error: Error) => void;
}

export class RequestInterceptor {
  private originalXHROpen: typeof XMLHttpRequest.prototype.open;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send;
  private originalFetch: typeof window.fetch;
  private isActive = false;
  private eventHandlers: Partial<InterceptorEvents> = {};
  private interceptedModels: ModelInfo[] = [];

  constructor() {
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    this.originalXHRSend = XMLHttpRequest.prototype.send;
    this.originalFetch = window.fetch;

    this.bindMethods();

    // 立即启动拦截器，但只拦截特定的 AI Studio API
    this.startImmediateInterception();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.interceptXHR = this.interceptXHR.bind(this);
    this.interceptFetch = this.interceptFetch.bind(this);
    this.handleXHRResponse = this.handleXHRResponse.bind(this);
  }

  /**
   * 立即启动拦截器（仅拦截 AI Studio API）
   */
  private startImmediateInterception(): void {
    logger.info('启动立即拦截器（仅 AI Studio API）...');

    const self = this;
    const listModelsUrl = 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/ListModels';
    const generateContentUrl = 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/GenerateContent';

    // 立即设置 XHR 拦截 - 完全按照原脚本的方式
    XMLHttpRequest.prototype.open = function (method: string, url: string | URL) {
      (this as any)._method = method;
      (this as any)._url = url.toString();
      return self.originalXHROpen.apply(this, arguments as any);
    };

    XMLHttpRequest.prototype.send = function (body?: Document | XMLHttpRequestBodyInit | null) {
      const xhr = this;
      const url = (xhr as any)._url;

      // 只拦截 AI Studio API 请求 - 完全按照原脚本的逻辑
      if (url === listModelsUrl) {
        xhr.onload = function () {
          if (xhr.readyState === 4 && xhr.status === 200) {
            try {
              const interceptedData = JSON.parse(xhr.response);
              logger.info('成功拦截到模型数据:', interceptedData);

              const models = self.parseModelsFromResponse(interceptedData);
              if (models.length > 0) {
                self.interceptedModels = models;
                logger.info(`拦截到模型列表: ${models.length} 个模型`);
                self.emit('modelsLoaded', models);
              } else {
                logger.warn('解析出的模型列表为空');
              }
            } catch (e) {
              logger.error('解析模型数据失败:', e);
            }
          }
        };
      } else if (url === generateContentUrl) {
        xhr.onprogress = function () {
          self.handleStreamResponse(xhr.responseText);
        };
      }

      // 使用 body 参数避免 TypeScript 警告
      return self.originalXHRSend.apply(xhr, [body]);
    };

    this.isActive = true;
    logger.info('立即拦截器已启动');
  }

  /**
   * 开始拦截
   */
  public start(): void {
    if (this.isActive) {
      logger.warn('请求拦截器已经激活');
      return;
    }

    logger.info('启动完整请求拦截器...');

    // 如果已经有立即拦截器，则扩展为完整拦截器
    this.interceptXHR();
    this.interceptFetch();

    this.isActive = true;
    logger.info('完整请求拦截器已启动');
  }

  /**
   * 停止拦截
   */
  public stop(): void {
    if (!this.isActive) {
      return;
    }

    logger.info('停止请求拦截器...');

    XMLHttpRequest.prototype.open = this.originalXHROpen;
    XMLHttpRequest.prototype.send = this.originalXHRSend;
    window.fetch = this.originalFetch;

    this.isActive = false;
    logger.info('请求拦截器已停止');
  }

  /**
   * 拦截 XMLHttpRequest
   */
  private interceptXHR(): void {
    const self = this;

    XMLHttpRequest.prototype.open = function (
      method: string,
      url: string | URL,
      async: boolean = true,
      user?: string | null,
      password?: string | null
    ) {
      // 存储请求信息
      (this as any)._method = method;
      (this as any)._url = url.toString();

      return self.originalXHROpen.call(this, method, url, async, user, password);
    };

    XMLHttpRequest.prototype.send = function (body?: Document | XMLHttpRequestBodyInit | null) {
      const xhr = this;
      const url = (xhr as any)._url;
      const method = (xhr as any)._method;

      // 检查是否是我们感兴趣的请求
      if (self.shouldInterceptRequest(url)) {
        logger.debug(`拦截 XHR 请求: ${method} ${url}`);

        // 设置响应处理器 - 使用 addEventListener 而不是直接赋值
        const handleLoad = function () {
          self.handleXHRResponse(xhr, url, body);
        };

        const handleProgress = function () {
          if (url.includes('GenerateContent')) {
            self.handleStreamResponse(xhr.responseText);
          }
        };

        xhr.addEventListener('load', handleLoad);
        xhr.addEventListener('progress', handleProgress);

        // 如果是 ListModels 请求，确保在 readyState 变化时也处理
        if (url.includes('ListModels')) {
          const handleReadyStateChange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
              self.handleXHRResponse(xhr, url, body);
            }
          };
          xhr.addEventListener('readystatechange', handleReadyStateChange);
        }
      }

      return self.originalXHRSend.call(this, body);
    };
  }

  /**
   * 拦截 Fetch API
   */
  private interceptFetch(): void {
    const self = this;

    window.fetch = async function (
      input: RequestInfo | URL,
      init?: RequestInit
    ): Promise<Response> {
      const url = typeof input === 'string' ? input : input.toString();

      if (self.shouldInterceptRequest(url)) {
        logger.debug(`拦截 Fetch 请求: ${init?.method || 'GET'} ${url}`);

        try {
          const response = await self.originalFetch.call(this, input, init);

          // 克隆响应以便我们可以读取它
          const clonedResponse = response.clone();

          // 异步处理响应
          self.handleFetchResponse(clonedResponse, url, init?.body);

          return response;
        } catch (error) {
          self.emit('error', error instanceof Error ? error : new Error(String(error)));
          throw error;
        }
      }

      return self.originalFetch.call(this, input, init);
    };
  }

  /**
   * 判断是否应该拦截请求
   */
  private shouldInterceptRequest(url: string): boolean {
    const listModelsUrl = 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/ListModels';
    const generateContentUrl = 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/GenerateContent';

    return url === listModelsUrl || url === generateContentUrl ||
      url.includes('ListModels') || url.includes('GenerateContent');
  }

  /**
   * 处理 XHR 响应
   */
  private handleXHRResponse(xhr: XMLHttpRequest, url: string, requestBody?: any): void {
    try {
      if (xhr.readyState === 4 && xhr.status === 200) {
        const responseText = xhr.responseText;

        if (url.includes('ListModels')) {
          this.handleModelsResponse(responseText);
        } else if (url.includes('GenerateContent')) {
          this.handleGenerateResponse(responseText, requestBody);
        }
      }
    } catch (error) {
      logger.error('处理 XHR 响应失败:', error);
      this.emit('error', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 处理 Fetch 响应
   */
  private async handleFetchResponse(response: Response, url: string, requestBody?: any): Promise<void> {
    try {
      const responseText = await response.text();

      if (url.includes('ListModels')) {
        this.handleModelsResponse(responseText);
      } else if (url.includes('GenerateContent')) {
        this.handleGenerateResponse(responseText, requestBody);
      }
    } catch (error) {
      logger.error('处理 Fetch 响应失败:', error);
      this.emit('error', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 处理模型列表响应
   */
  private handleModelsResponse(responseText: string): void {
    try {
      if (!isValidJson(responseText)) {
        logger.warn('模型列表响应不是有效的 JSON');
        return;
      }

      const data = JSON.parse(responseText);
      const models = this.parseModelsFromResponse(data);

      if (models.length > 0) {
        this.interceptedModels = models;
        logger.info(`拦截到模型列表: ${models.length} 个模型`);
        this.emit('modelsLoaded', models);
      }
    } catch (error) {
      logger.error('解析模型列表响应失败:', error);
      this.emit('error', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 从响应中解析模型列表
   */
  private parseModelsFromResponse(data: any): ModelInfo[] {
    const models: ModelInfo[] = [];

    try {
      // 根据 AI Studio 的响应格式解析模型
      if (data && Array.isArray(data[0])) {
        for (const modelData of data[0]) {
          if (Array.isArray(modelData) && modelData.length >= 4) {
            const model: ModelInfo = {
              id: modelData[0] || '',
              name: modelData[1] || '',
              displayName: modelData[3] || modelData[1] || '',
              description: modelData[2] || '',
            };

            if (model.id && model.name) {
              models.push(model);
            }
          }
        }
      }
    } catch (error) {
      logger.error('解析模型数据失败:', error);
    }

    return models;
  }

  /**
   * 处理生成内容响应
   */
  private handleGenerateResponse(responseText: string, requestBody?: any): void {
    try {
      // 解析请求
      if (requestBody) {
        const request = this.parseGenerateRequest(requestBody);
        if (request) {
          this.emit('requestSent', request);
        }
      }

      // 解析响应
      const response = this.parseGenerateResponse(responseText);
      if (response) {
        this.emit('responseReceived', response);
      }
    } catch (error) {
      logger.error('处理生成内容响应失败:', error);
      this.emit('error', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 处理流式响应
   */
  private handleStreamResponse(responseText: string): void {
    try {
      // 解析流式响应中的增量内容
      const lines = responseText.split('\n');

      for (const line of lines) {
        if (line.trim() && line.includes('wrb.fr')) {
          try {
            const parsed = JSON.parse(line);
            const content = this.extractStreamContent(parsed);
            if (content) {
              this.emit('streamChunk', content);
            }
          } catch {
            // 忽略解析错误，继续处理下一行
          }
        }
      }
    } catch (error) {
      logger.error('处理流式响应失败:', error);
    }
  }

  /**
   * 从流式数据中提取内容
   */
  private extractStreamContent(data: any): string | null {
    try {
      // 根据 AI Studio 的流式响应格式提取内容
      if (data && data[0] && data[0][2]) {
        const mainPart = JSON.parse(data[0][2]);
        if (mainPart && mainPart[4] && mainPart[4][0] && mainPart[4][0][1] && mainPart[4][0][1][0]) {
          return mainPart[4][0][1][0];
        }
      }
    } catch (error) {
      logger.debug('提取流式内容失败:', error);
    }

    return null;
  }

  /**
   * 解析生成请求
   */
  private parseGenerateRequest(requestBody: any): GenerateContentRequest | null {
    try {
      if (typeof requestBody === 'string') {
        return JSON.parse(requestBody);
      }
      return requestBody;
    } catch (error) {
      logger.debug('解析生成请求失败:', error);
      return null;
    }
  }

  /**
   * 解析生成响应
   */
  private parseGenerateResponse(responseText: string): GenerateContentResponse | null {
    try {
      if (isValidJson(responseText)) {
        return JSON.parse(responseText);
      }

      // 处理非标准格式的响应
      const lines = responseText.split('\n');
      const jsonLine = lines.find(line => line.trim().startsWith('['));

      if (jsonLine) {
        return JSON.parse(jsonLine);
      }
    } catch (error) {
      logger.debug('解析生成响应失败:', error);
    }

    return null;
  }

  /**
   * 注册事件处理器
   */
  public on<K extends keyof InterceptorEvents>(
    event: K,
    handler: InterceptorEvents[K]
  ): void {
    this.eventHandlers[event] = handler;
  }

  /**
   * 移除事件处理器
   */
  public off<K extends keyof InterceptorEvents>(event: K): void {
    delete this.eventHandlers[event];
  }

  /**
   * 触发事件
   */
  private emit<K extends keyof InterceptorEvents>(
    event: K,
    ...args: Parameters<NonNullable<InterceptorEvents[K]>>
  ): void {
    const handler = this.eventHandlers[event];
    if (handler) {
      try {
        (handler as any)(...args);
      } catch (error) {
        logger.error(`事件处理器执行失败 (${event}):`, error);
      }
    }
  }

  /**
   * 获取拦截到的模型列表
   */
  public getInterceptedModels(): ModelInfo[] {
    return [...this.interceptedModels];
  }

  /**
   * 检查是否已拦截到模型列表
   */
  public hasModels(): boolean {
    return this.interceptedModels.length > 0;
  }

  /**
   * 清除拦截到的数据
   */
  public clearInterceptedData(): void {
    this.interceptedModels = [];
  }
}
